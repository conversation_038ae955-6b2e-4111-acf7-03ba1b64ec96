import type React from "react"
import type { Metada<PERSON> } from "next"
import { Inter } from "next/font/google"
import "./globals.css"

const inter = Inter({ subsets: ["latin"] })

export const metadata: Metadata = {
  title: "Anvie - Thực phẩm hữu cơ Việt Nam",
  description:
    "C<PERSON><PERSON> hàng thực phẩm hữu cơ Anvie chuyên cung cấp rau củ quả, sữa thực vật, ngũ cốc và các sản phẩm chay tự nhiên, an toàn cho sức khỏe.",
  keywords: "anvie, thực phẩm hữu cơ, rau củ quả, sữa thực vật, đồ chay, organic food, healthy food, việt nam",
    generator: 'v0.dev'
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="vi">
      <body className={inter.className}>{children}</body>
    </html>
  )
}
