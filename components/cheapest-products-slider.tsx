"use client"

import { useState, useEffect } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Star } from "lucide-react"
import Image from "next/image"
import Link from "next/link"

const cheapProducts = [
  { id: 1, name: "<PERSON><PERSON> rốt hữu cơ", price: 15000, image: "/placeholder.svg?height=200&width=200", rating: 4.5 },
  { id: 2, name: "<PERSON><PERSON> muống", price: 12000, image: "/placeholder.svg?height=200&width=200", rating: 4.3 },
  { id: 3, name: "<PERSON><PERSON><PERSON> t<PERSON><PERSON>", price: 18000, image: "/placeholder.svg?height=200&width=200", rating: 4.4 },
  { id: 4, name: "<PERSON>ắ<PERSON> cải", price: 20000, image: "/placeholder.svg?height=200&width=200", rating: 4.2 },
  { id: 5, name: "<PERSON><PERSON> chua", price: 22000, image: "/placeholder.svg?height=200&width=200", rating: 4.6 },
  { id: 6, name: "<PERSON><PERSON><PERSON> chu<PERSON>", price: 16000, image: "/placeholder.svg?height=200&width=200", rating: 4.1 },
  { id: 7, name: "Rau cải", price: 14000, image: "/placeholder.svg?height=200&width=200", rating: 4.3 },
  { id: 8, name: "Hành tây", price: 19000, image: "/placeholder.svg?height=200&width=200", rating: 4.0 },
  { id: 9, name: "Ớt chuông", price: 25000, image: "/placeholder.svg?height=200&width=200", rating: 4.5 },
  { id: 10, name: "Bông cải xanh", price: 28000, image: "/placeholder.svg?height=200&width=200", rating: 4.7 },
]

export function CheapestProductsSlider() {
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isAnimating, setIsAnimating] = useState(true)

  useEffect(() => {
    if (!isAnimating) return

    const timer = setInterval(() => {
      setCurrentIndex((prevIndex) => (prevIndex + 1) % cheapProducts.length)
    }, 3000)

    return () => clearInterval(timer)
  }, [isAnimating])

  const handleMouseEnter = () => setIsAnimating(false)
  const handleMouseLeave = () => setIsAnimating(true)

  return (
    <div className="overflow-hidden" onMouseEnter={handleMouseEnter} onMouseLeave={handleMouseLeave}>
      <div
        className="flex transition-transform duration-1000 ease-in-out"
        style={{
          transform: `translateX(-${(currentIndex * 100) / 5}%)`,
          width: `${(cheapProducts.length * 100) / 5}%`,
        }}
      >
        {[...cheapProducts, ...cheapProducts].map((product, index) => (
          <div key={`${product.id}-${index}`} className="w-1/5 flex-shrink-0 px-2">
            <Link href={`/products/${product.id}`}>
              <Card className="hover:shadow-lg transition-shadow cursor-pointer border-green-200 hover:border-green-400">
                <CardContent className="p-4">
                  <div className="relative mb-3">
                    <Image
                      src={product.image || "/placeholder.svg"}
                      alt={product.name}
                      width={200}
                      height={200}
                      className="rounded-lg object-cover w-full h-32"
                    />
                    <Badge className="absolute top-2 left-2 bg-red-500 text-xs">Giá rẻ</Badge>
                  </div>
                  <h4 className="font-semibold text-green-800 mb-2 text-sm line-clamp-2">{product.name}</h4>
                  <div className="flex items-center mb-2">
                    <Star className="w-3 h-3 text-yellow-400 fill-current" />
                    <span className="ml-1 text-xs text-gray-600">{product.rating}</span>
                  </div>
                  <p className="text-lg font-bold text-green-600">{product.price.toLocaleString("vi-VN")}đ</p>
                </CardContent>
              </Card>
            </Link>
          </div>
        ))}
      </div>
    </div>
  )
}
